package com.ksxz.search.mapper;

import java.util.List;
import com.ksxz.search.domain.MeetingRoom;

/**
 * 会议室信息查询Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface MeetingRoomMapper 
{
    /**
     * 查询会议室信息查询
     * 
     * @param roomId 会议室信息查询主键
     * @return 会议室信息查询
     */
    public MeetingRoom selectMeetingRoomByRoomId(Long roomId);

    /**
     * 查询会议室信息查询列表
     * 
     * @param meetingRoom 会议室信息查询
     * @return 会议室信息查询集合
     */
    public List<MeetingRoom> selectMeetingRoomList(MeetingRoom meetingRoom);

    /**
     * 新增会议室信息查询
     * 
     * @param meetingRoom 会议室信息查询
     * @return 结果
     */
    public int insertMeetingRoom(MeetingRoom meetingRoom);

    /**
     * 修改会议室信息查询
     * 
     * @param meetingRoom 会议室信息查询
     * @return 结果
     */
    public int updateMeetingRoom(MeetingRoom meetingRoom);

    /**
     * 删除会议室信息查询
     * 
     * @param roomId 会议室信息查询主键
     * @return 结果
     */
    public int deleteMeetingRoomByRoomId(Long roomId);

    /**
     * 批量删除会议室信息查询
     * 
     * @param roomIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMeetingRoomByRoomIds(Long[] roomIds);
}
