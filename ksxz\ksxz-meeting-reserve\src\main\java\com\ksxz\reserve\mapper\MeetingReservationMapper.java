package com.ksxz.reserve.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ksxz.reserve.domain.MeetingReservation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会议室预约申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Mapper
public interface MeetingReservationMapper 
{
    /**
     * 查询会议室预约申请
     * 
     * @param reservationId 会议室预约申请主键
     * @return 会议室预约申请
     */
    public MeetingReservation selectMeetingReservationByReservationId(Long reservationId);

    /**
     * 查询会议室预约申请列表
     * 
     * @param meetingReservation 会议室预约申请
     * @return 会议室预约申请集合
     */
    public List<MeetingReservation> selectMeetingReservationList(MeetingReservation meetingReservation);

    /**
     * 新增会议室预约申请
     * 
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    public int insertMeetingReservation(MeetingReservation meetingReservation);

    /**
     * 修改会议室预约申请
     * 
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    public int updateMeetingReservation(MeetingReservation meetingReservation);

    /**
     * 删除会议室预约申请
     * 
     * @param reservationId 会议室预约申请主键
     * @return 结果
     */
    public int deleteMeetingReservationByReservationId(Long reservationId);

    /**
     * 批量删除会议室预约申请
     * 
     * @param reservationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMeetingReservationByReservationIds(Long[] reservationIds);

    /**
     * 判断会议室是否可用
     * @param roomId
     * @param startTime
     * @param endTime
     * @param excludeReservationId
     * @return
     */
    List<MeetingReservation> selectConflictingReservations(@Param("roomId") Long roomId,
                                                           @Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime,
                                                           @Param("excludeReservationId") Long excludeReservationId);


}
