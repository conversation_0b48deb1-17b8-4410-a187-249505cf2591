<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksxz.search.mapper.MeetingRoomMapper">
    
    <resultMap type="MeetingRoom" id="MeetingRoomResult">
        <result property="roomId"    column="room_id"    />
        <result property="roomName"    column="room_name"    />
        <result property="capacity"    column="capacity"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="autoApprove" column="auto_approve" />
    </resultMap>

    <sql id="selectMeetingRoomVo">
        select room_id, room_name, capacity, image_url, status, create_by, create_time, update_by, update_time, remark from meeting_room
    </sql>

    <select id="selectMeetingRoomList" parameterType="MeetingRoom" resultMap="MeetingRoomResult">
        <include refid="selectMeetingRoomVo"/>
        <where>  
            <if test="roomName != null  and roomName != ''"> and room_name like concat('%', #{roomName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="roomId !=null"> and room_id = #{roomId}</if>
            <if test="capacity !=null"> and capacity = #{capacity}</if>
        </where>
    </select>
    
    <select id="selectMeetingRoomByRoomId" parameterType="Long" resultMap="MeetingRoomResult">
        <include refid="selectMeetingRoomVo"/>
        where room_id = #{roomId}
    </select>

    <insert id="insertMeetingRoom" parameterType="MeetingRoom" useGeneratedKeys="true" keyProperty="roomId">
        insert into meeting_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">room_name,</if>
            <if test="capacity != null">capacity,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="autoApprove != null">auto_approve,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">#{roomName},</if>
            <if test="capacity != null">#{capacity},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="autoApprove != null">#{autoApprove},</if>
         </trim>
    </insert>

    <update id="updateMeetingRoom" parameterType="MeetingRoom">
        update meeting_room
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">room_name = #{roomName},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="autoApprove != null">auto_approve = #{autoApprove},</if>
        </trim>
        where room_id = #{roomId}
    </update>

    <delete id="deleteMeetingRoomByRoomId" parameterType="Long">
        delete from meeting_room where room_id = #{roomId}
    </delete>

    <delete id="deleteMeetingRoomByRoomIds" parameterType="String">
        delete from meeting_room where room_id in 
        <foreach item="roomId" collection="array" open="(" separator="," close=")">
            #{roomId}
        </foreach>
    </delete>
</mapper>