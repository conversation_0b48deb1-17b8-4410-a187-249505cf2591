<template>
  <div class="recent-list">
    <div class="list-header">
      <h3>最近预约</h3>
      <el-button link size="small" @click="handleViewAll">查看全部</el-button>
    </div>
    
    <div class="list-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!recentList || recentList.length === 0" class="empty-state">
        <el-icon><DocumentRemove /></el-icon>
        <p>暂无预约记录</p>
      </div>
      
      <!-- 预约列表 -->
      <div v-else class="reservation-list">
        <div 
          v-for="(item, index) in recentList" 
          :key="item.reservationId || index"
          class="reservation-item"
        >
          <div class="item-left">
            <div class="room-info">
              <span class="room-name">{{ item.roomName || '未知会议室' }}</span>
              <el-tag 
                :type="getStatusType(item.status)" 
                size="small"
                class="status-tag"
              >
                {{ getStatusText(item.status) }}
              </el-tag>
            </div>
            <div class="time-info">
              <el-icon><Clock /></el-icon>
              <span>{{ formatTimeRange(item.startTime, item.endTime) }}</span>
            </div>
          </div>
          <div class="item-right">
            <div v-if="item.status === 1">
              <el-button
                type="success"
                size="small"
                icon="Check"
                @click="handleApprove(item, 2)"
                :disabled="!canApprove(item)"
                v-hasPermi="['reserve:reservation:approve']"
                class="action-btn-small"
                >通过</el-button>

                <el-button
                type="danger"
                size="small"
                icon="Close"
                @click="handleApprove(item, 3)"
                :disabled="!canReject(item)"
                v-hasPermi="['reserve:reservation:approve']"
                class="action-btn-small"
                >拒绝</el-button>
            </div>
            <div v-else class="create-time">
              {{ formatCreateTime(item.createTime) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter  } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Loading,
  DocumentRemove,
  Clock,
  Check,
  Close
} from '@element-plus/icons-vue'
import { approveReservation } from '@/api/reserve/reservation'

//Props定义
const props = defineProps({
  reservationData: {
    type: Array,
    default: () => []
  },
    roomData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

//事件定义
const emit = defineEmits(['refresh'])

//创建会议室ID到名称的映射
const roomMap = computed(() => {
  const map = {}
  if (props.roomData && props.roomData.length > 0) {
    props.roomData.forEach(room => {
      map[room.roomId || room.id] = room.roomName || room.name
    })
  }
  console.log('会议室映射:', map) // 调试信息
  return map
})

//添加会议室名称
const recentList = computed(() => {
  if (!props.reservationData || props.reservationData.length === 0) return []
  
  return props.reservationData
    .map(reservation => {
      console.log('预约roomId:', reservation.roomId, '对应名称:', roomMap.value[reservation.roomId])
      
      return {
        ...reservation,
        roomName: roomMap.value[reservation.roomId] || `会议室${reservation.roomId}` || '未知会议室'
      }
    })
    .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    .slice(0, 5)
})


//状态样式映射
const getStatusType = (status) => {
  const statusMap = {
    0: 'info' ,// 已取消 - 灰色
    1: 'warning', // 待审核 - 黄色
    2: 'success', // 已通过 - 绿色
    3: 'danger', // 已拒绝 - 红色
  }
  return statusMap[status] || ''
}

//状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    0: '已取消', 
    1: '待审核',    
    2: '已通过',
    3: '已拒绝'
  }
  return statusMap[status] || '未知状态'
}

//格式化时间范围
const formatTimeRange = (startTime, endTime) => {
  if (!startTime || !endTime) return '时间待定'
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  const formatTime = (date) => {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 如果是同一天，只显示日期一次
  if (start.toDateString() === end.toDateString()) {
    return `${start.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })} ${start.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}-${end.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
  }
  
  return `${formatTime(start)} ~ ${formatTime(end)}`
}

//格式化创建时间
const formatCreateTime = (createTime) => {
  if (!createTime) return ''
  
  const date = new Date(createTime)
  const now = new Date()
  
  // 比较年、月、日是否相同来判断是否为今天
  const isToday = date.getFullYear() === now.getFullYear() &&
                  date.getMonth() === now.getMonth() &&
                  date.getDate() === now.getDate()
                  
  // 计算昨天的日期
  const yesterday = new Date(now)
  yesterday.setDate(now.getDate() - 1)
  
  // 比较是否为昨天
  const isYesterday = date.getFullYear() === yesterday.getFullYear() &&
                     date.getMonth() === yesterday.getMonth() &&
                     date.getDate() === yesterday.getDate()
                     
  const diffTime = now - date
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (isToday) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (isYesterday) {
    return '昨天' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

const router = useRouter()

//事件处理
//
const handleViewAll = () => {
  router.push('../../reserve/reservation');
}

const handleItemClick = (item) => {
  // TODO: 查看预约详情
  console.log('查看预约详情:', item)
  ElMessage.info(`查看预约详情: ${item.roomName}`)
}

//审批处理函数
const handleApprove = async (item, status) => {
  const actionText = status === 2 ? '通过' : '拒绝'

  try {
    await ElMessageBox.confirm(
      `确认要${actionText}该预约吗?`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    )

    const data = {
      reservationId: item.reservationId,
      status: status,
      approveComment: `${actionText}审批`
    }

    await approveReservation(data)
    ElMessage.success(`${actionText}成功`)

    // 通知父组件刷新数据
    emit('refresh')

  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error(`${actionText}失败: ` + (error.message || error))
    }
  }
}

//权限检查函数
const canApprove = (item) => {
  return item.status === 1 && item.reservationId
}

const canReject = (item) => {
  return item.status === 1 && item.reservationId
}
</script>

<style scoped>
.recent-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.list-content {
  flex: 1;
  padding: 16px 0;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.loading-state .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #dcdfe6;
}

.reservation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reservation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.reservation-item:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.item-left {
  flex: 1;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.room-name {
  font-weight: 500;
  color: #303133;
}

.status-tag {
  font-size: 12px;
}

.time-info,
.user-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.time-info .el-icon,
.user-info .el-icon {
  font-size: 14px;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.arrow-icon {
  color: #c0c4cc;
  font-size: 14px;
}

.action-btn-small {
  margin-left: 8px;
  padding: 4px 8px;
  font-size: 12px;
}

.action-btn-small:first-child {
  margin-left: 0;
}
</style>
