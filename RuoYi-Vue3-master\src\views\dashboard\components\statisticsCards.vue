<template>
  <div class="statistics-section">
    <el-row :gutter="20">

        <!-- 今日预约 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <div class="stat-card today-reservations" v-loading="loading">
                <div class="stat-icon">
                    <el-icon><Calendar /></el-icon>
                </div>
                <div class="stat-content">
                    <div class="stat-title">今日预约</div>
                    <div class="stat-value">{{ statistics.todayReservations }}</div>
                    <!-- <div class="stat-desc">暂无预约接口</div> -->
                </div>
            </div>
        </el-col>
      
        <!-- 待审核 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <div class="stat-card pending-review" v-loading="loading">
                <div class="stat-icon">
                    <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-content">
                    <div class="stat-title">待审核</div>
                    <div class="stat-value">{{ statistics.pendingReservations }}</div>
                    <!-- <div class="stat-desc">暂无预约接口</div> -->
                </div>
            </div>
        </el-col>
      
        <!-- 可预约会议室 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <div class="stat-card available-rooms" v-loading="loading">
                <div class="stat-icon">
                    <el-icon><OfficeBuilding /></el-icon>
                </div>
                <div class="stat-content">
                    <div class="stat-title">可预约会议室</div>
                    <div class="stat-value">{{ statistics.availableRooms }}</div>
                    <div class="stat-desc">
                    <!-- 总计 {{ statistics.totalRooms }} 间 -->
                    <!-- <span v-if="statistics.unavailableRooms > 0" class="unavailable-hint">
                        ({{ statistics.unavailableRooms }}间不可用)
                    </span> -->
                    </div>
                </div>
            </div>
        </el-col>
      
        <!-- 总用户数 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <div class="stat-card total-users" v-loading="loading">
                <div class="stat-icon">
                    <el-icon><User /></el-icon>
                </div>
                <div class="stat-content">
                    <div class="stat-title">总用户数</div>
                    <div class="stat-value">{{ statistics.totalUsers }}</div>
                    <!-- <div class="stat-desc">
                    活跃 {{ statistics.activeUsers }} 人
                    </div> -->
                </div>
            </div>
        </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { Calendar, Clock, OfficeBuilding, User } from '@element-plus/icons-vue'

// 接收父组件传递的数据
defineProps({
  statistics: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.statistics-section {
  margin-bottom: 20px;
  
  .stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    height: 100px;
    margin-bottom: 20px;
    position: relative;
    
    &.today-reservations .stat-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    &.pending-review .stat-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    &.available-rooms .stat-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    &.total-users .stat-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      .el-icon {
        font-size: 24px;
        color: white;
      }
    }
    
    .stat-content {
      flex: 1;
      
      .stat-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .stat-desc {
        font-size: 12px;
        color: #C0C4CC;
      }
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
    }
  }
}
</style>
