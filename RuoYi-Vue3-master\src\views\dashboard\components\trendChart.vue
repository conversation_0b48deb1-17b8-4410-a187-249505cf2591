<template>
  <div class="trend-chart">
    <div class="chart-header">
      <h3 class="chart-title">预约趋势</h3>
      <div class="chart-controls">
        <el-radio-group v-model="timeRange" size="small" @change="updateChart">
          <el-radio-button value="7">近7天</el-radio-button>
          <el-radio-button value="30">近30天</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    
    <div class="chart-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>数据加载中...</span>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!hasData" class="empty-state">
        <el-icon><TrendCharts /></el-icon>
        <p>暂无预约统计数据</p>
        <span>数据统计需要一定时间积累</span>
      </div>
      
      <!-- 图表容器 -->
      <div v-else ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { Loading, TrendCharts } from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  reservationData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const chartRef = ref(null)
const timeRange = ref('7')
let chartInstance = null

// 计算是否有数据
const hasData = computed(() => {
  const result = props.reservationData && props.reservationData.length > 0
  console.log('hasData计算:', result, '数据:', props.reservationData?.length || 0)
  return result
})

// 处理趋势数据
const getTrendData = () => {
  // 提前检查数据有效性
  if (!props.reservationData || props.reservationData.length === 0) {
    console.log('无数据，返回空图表数据')
    return {
      categories: [],
      totalData: [],
      pendingData: [],
      approvedData: [],
      rejectedData: []
    }
  }

  const days = parseInt(timeRange.value)
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - days + 1)
  
  // 生成日期数组
  const dateArray = []
  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    dateArray.push(new Date(currentDate).toISOString().split('T')[0])
    currentDate.setDate(currentDate.getDate() + 1)
  }
  
  // 统计每天的预约数量
  const reservationCounts = {}
  const statusCounts = {}
  
  // 初始化计数器
  dateArray.forEach(date => {
    reservationCounts[date] = 0
    statusCounts[date] = { pending: 0, approved: 0, rejected: 0, cancelled: 0 }
  })
  
  // 统计实际数据
  props.reservationData.forEach(reservation => {
    // 根据您的数据结构，使用 startTime 字段
    const reservationDate = reservation.startTime ? reservation.startTime.split(' ')[0] : null
    if (reservationDate && reservationCounts.hasOwnProperty(reservationDate)) {
      reservationCounts[reservationDate]++
      
      // 按状态分类统计
      switch (reservation.status) {
        case 0:
          statusCounts[reservationDate].cancelled++
          break
        case 1:
          statusCounts[reservationDate].pending++
          break
        case 2:
          statusCounts[reservationDate].approved++
          break
        case 3:
          statusCounts[reservationDate].rejected++
          break
      }
    }
  })
  
  // 转换为图表需要的格式
  const categories = dateArray.map(date => {
    const d = new Date(date)
    return `${d.getMonth() + 1}/${d.getDate()}`
  })
  
  const totalData = dateArray.map(date => reservationCounts[date])
  const pendingData = dateArray.map(date => statusCounts[date].pending)
  const approvedData = dateArray.map(date => statusCounts[date].approved)
  const rejectedData = dateArray.map(date => statusCounts[date].rejected)
  
  return {
    categories,
    totalData,
    pendingData,
    approvedData,
    rejectedData
  }
}

// 初始化图表
const initChart = () => {
  console.log('尝试初始化图表', {
    chartRef: !!chartRef.value,
    hasData: hasData.value,
    dataLength: props.reservationData?.length
  })
  
  // 先确保DOM元素已经存在
  if (!chartRef.value) {
    console.log('图表容器不存在')
    return
  }
  
  // 强制销毁旧实例
  if (chartInstance) {
    console.log('销毁旧图表实例')
    chartInstance.dispose()
    chartInstance = null
  }
  
  // 创建新实例并直接更新
  console.log('创建新图表实例')
  chartInstance = echarts.init(chartRef.value)
  
  // 即使没有数据也要初始化图表
  updateChart()
}

// 更新图表
const updateChart = () => {
  console.log('更新图表', {
    chartInstance: !!chartInstance,
    hasData: hasData.value,
    dataLength: props.reservationData?.length
  })
  
  if (!chartInstance) {
    console.log('图表实例不存在，尝试重新初始化')
    nextTick(() => initChart())
    return
  }
  
  const { categories, totalData, pendingData, approvedData, rejectedData } = getTrendData()
  
  // 图表配置
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params) {
        let result = `<div style="margin-bottom: 5px; font-weight: bold">${params[0].axisValue}</div>`
        params.forEach(param => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 2px">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px"></span>
            <span style="flex: 1">${param.seriesName}:</span>
            <span style="font-weight: bold; margin-left: 10px">${param.value}条</span>
          </div>`
        })
        return result
      }
    },
    legend: {
      data: ['总预约', '待审核', '已通过', '已拒绝'],
      top: 10,
      itemGap: 20,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: categories,
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '预约数量',
      nameTextStyle: {
        color: '#666',
        fontSize: 11
      },
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '总预约',
        type: 'line',
        data: totalData,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409eff'
        },
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
          ])
        }
      },
      {
        name: '待审核',
        type: 'bar',
        data: pendingData,
        itemStyle: {
          color: '#909399'
        }
      },
      {
        name: '已通过',
        type: 'bar',
        data: approvedData,
        itemStyle: {
          color: '#67c23a'  
        }
      },
      {
        name: '已拒绝',
        type: 'bar',
        data: rejectedData,
        itemStyle: {
          color: '#f56c6c'
        }
      }
    ]
  }
  
  try {
    console.log('📊 设置图表数据')
    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('设置图表选项失败:', error)
  }
}

// 监听数据变化
watch([
  () => props.reservationData,
  () => props.loading
], ([newData, newLoading]) => {
  console.log('数据或加载状态变化:', {
    dataLength: newData?.length || 0,
    loading: newLoading,
    hasData: newData && newData.length > 0,
    chartExists: !!chartInstance
  })
  
  // 数据加载完成后
  if (!newLoading) {
    // 添加延迟确保DOM已更新
    nextTick(() => {
      // 无论是否有数据都尝试初始化/更新图表
      if (!chartInstance && hasData.value) {
        console.log('数据更新后初始化图表')
        initChart} else {
        console.log('数据更新后更新图表')
        updateChart()
      }
    })
  }
}, { deep: true, immediate: true })

//响应式处理
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

//组件挂载
onMounted(() => {
  console.log('TrendChart组件已挂载')
  
  // 移除延迟初始化
  setTimeout(() => {
    console.log('延迟初始化检查', {
      dataLength: props.reservationData?.length || 0,
      loading: props.loading,
      hasData: hasData.value
    })
    
    if (!props.loading) {
      initChart()
    }
  }, 100)
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.trend-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-content {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.loading-state .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #dcdfe6;
}

.empty-state p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.empty-state span {
  font-size: 12px;
  color: #909399;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>