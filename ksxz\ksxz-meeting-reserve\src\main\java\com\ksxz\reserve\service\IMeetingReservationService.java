package com.ksxz.reserve.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ksxz.common.core.domain.AjaxResult;
import com.ksxz.reserve.domain.MeetingReservation;

/**
 * 会议室预约申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IMeetingReservationService 
{
    /**
     * 查询会议室预约申请
     * 
     * @param reservationId 会议室预约申请主键
     * @return 会议室预约申请
     */
    public MeetingReservation selectMeetingReservationByReservationId(Long reservationId);

    /**
     * 查询会议室预约申请列表
     * 
     * @param meetingReservation 会议室预约申请
     * @return 会议室预约申请集合
     */
    public List<MeetingReservation> selectMeetingReservationList(MeetingReservation meetingReservation);

    /**
     * 新增会议室预约申请
     * 
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    public int insertMeetingReservation(MeetingReservation meetingReservation);

    /**
     * 修改会议室预约申请
     * 
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    public int updateMeetingReservation(MeetingReservation meetingReservation);

    /**
     * 批量删除会议室预约申请
     * 
     * @param reservationIds 需要删除的会议室预约申请主键集合
     * @return 结果
     */
    public int deleteMeetingReservationByReservationIds(Long[] reservationIds);

    /**
     * 删除会议室预约申请信息
     * 
     * @param reservationId 会议室预约申请主键
     * @return 结果
     */
    public int deleteMeetingReservationByReservationId(Long reservationId);

    /**
     * 审批会议室预约申请
     * @param meetingReservation
     * @return
     */
    AjaxResult approveReservation(MeetingReservation meetingReservation);

    /**
     * 批量审批会议室预约申请
     * @param reservations
     * @return
     */
    AjaxResult batchApproveReservation(List<MeetingReservation> reservations);

    /**
     * 强制取消会议室预约申请
     * @param reservationIds
     * @return
     */
    AjaxResult forceCancelReservation(Long[] reservationIds);


    /**
     * 取消会议室预约申请
     * @param reservationId
     * @return
     */
    int cancelReservation(Long reservationId);

    /**
     * 用户提交会议室预约申请
     *
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    public int userInsertMeetingReservation(MeetingReservation meetingReservation);

    /**
     * 查询当前用户的预约列表
     * @param meetingReservation
     * @return
     */
    List<MeetingReservation> selectUserReservationList(MeetingReservation meetingReservation);

    /**
     * 查询当前用户的预约详情
     * @param reservationId
     * @param userId
     * @return
     */
    MeetingReservation selectUserReservationByReservationId(Long reservationId, Long userId);

    /**
     * 用户取消自己的预约
     * @param reservationId
     * @param userId
     * @return
     */
    int userCancelReservation(Long reservationId, Long userId);

    /**
     * 检查会议室是否可用
     * @param roomId 会议室ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeReservationId 排除的预约ID(用于修改时检查)
     * @return 是否可用
     */
    boolean isRoomAvailable(Long roomId, Date startTime, Date endTime, Long excludeReservationId);


}
