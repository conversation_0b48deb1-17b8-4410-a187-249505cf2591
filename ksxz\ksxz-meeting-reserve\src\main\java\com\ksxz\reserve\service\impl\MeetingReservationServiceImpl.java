package com.ksxz.reserve.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ksxz.common.core.domain.AjaxResult;
import com.ksxz.common.utils.DateUtils;
import com.ksxz.common.utils.SecurityUtils;
import com.ksxz.reserve.service.IConfigService;
import com.ksxz.search.domain.MeetingRoom;
import com.ksxz.search.mapper.MeetingRoomMapper;
import com.ksxz.system.domain.SysNotice;
import com.ksxz.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ksxz.reserve.mapper.MeetingReservationMapper;
import com.ksxz.reserve.domain.MeetingReservation;
import com.ksxz.reserve.service.IMeetingReservationService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会议室预约申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class MeetingReservationServiceImpl implements IMeetingReservationService 
{
    @Autowired
    private MeetingReservationMapper meetingReservationMapper;

    /**
     * 查询会议室预约申请
     * 
     * @param reservationId 会议室预约申请主键
     * @return 会议室预约申请
     */
    @Override
    public MeetingReservation selectMeetingReservationByReservationId(Long reservationId)
    {
        return meetingReservationMapper.selectMeetingReservationByReservationId(reservationId);
    }

    /**
     * 查询会议室预约申请列表
     * 
     * @param meetingReservation 会议室预约申请
     * @return 会议室预约申请
     */
    @Override
    public List<MeetingReservation> selectMeetingReservationList(MeetingReservation meetingReservation)
    {
        return meetingReservationMapper.selectMeetingReservationList(meetingReservation);
    }

    /**
     * 新增会议室预约申请
     * 
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    @Autowired
    private IConfigService configService;

    @Override
    public int insertMeetingReservation(MeetingReservation meetingReservation)
    {
        // 校验时间合法性
        if (!meetingReservation.getStartTime().before(meetingReservation.getEndTime())) {
            throw new RuntimeException("开始时间不得晚于或等于结束时间");
        }
        //检查会议室是否可用
        if(!isRoomAvailable(meetingReservation.getRoomId(), meetingReservation.getStartTime(), meetingReservation.getEndTime(), null)){
            throw new RuntimeException("该会议室在选定时间段已被预约");
        }
        //检查是否启用自动审批
        String autoApproveEnabled = configService.getConfigValue("meeting.autoApprove.enabled");
        if("true".equals(autoApproveEnabled)){
            meetingReservation.setStatus(2L);
        }else{
            meetingReservation.setStatus(1L);
        }
        meetingReservation.setCreateTime(DateUtils.getNowDate());
        return meetingReservationMapper.insertMeetingReservation(meetingReservation);
    }

    /**
     * 修改会议室预约申请
     * 
     * @param meetingReservation 会议室预约申请
     * @return 结果
     */
    @Override
    public int updateMeetingReservation(MeetingReservation meetingReservation)
    {
        // 校验时间合法性
        if (!meetingReservation.getStartTime().before(meetingReservation.getEndTime())) {
            throw new RuntimeException("开始时间不得晚于或等于结束时间");
        }
        MeetingReservation existing = meetingReservationMapper.selectMeetingReservationByReservationId(meetingReservation.getReservationId());
        //如果修改了会议室时间，需要检查冲突
        if(!existing.getRoomId().equals(meetingReservation.getRoomId()) ||
            !existing.getStartTime().equals(meetingReservation.getStartTime()) ||
            !existing.getEndTime().equals(meetingReservation.getEndTime())){
            if(!isRoomAvailable(meetingReservation.getRoomId(), meetingReservation.getStartTime(), meetingReservation.getEndTime(), existing.getReservationId())){
                throw new RuntimeException("该会议室在选定时间段已被预约");
            }
        }
        meetingReservation.setUpdateTime(DateUtils.getNowDate());
        return meetingReservationMapper.updateMeetingReservation(meetingReservation);
    }

    /**
     * 批量删除会议室预约申请
     * 
     * @param reservationIds 需要删除的会议室预约申请主键
     * @return 结果
     */
    @Override
    public int deleteMeetingReservationByReservationIds(Long[] reservationIds)
    {
        return meetingReservationMapper.deleteMeetingReservationByReservationIds(reservationIds);
    }

    /**
     * 删除会议室预约申请信息
     * 
     * @param reservationId 会议室预约申请主键
     * @return 结果
     */
    @Override
    public int deleteMeetingReservationByReservationId(Long reservationId)
    {
        return meetingReservationMapper.deleteMeetingReservationByReservationId(reservationId);
    }

    /**
     * 审批预约申请
     * @param meetingReservation
     * @return
     */
    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private MeetingRoomMapper meetingRoomMapper;
    @Override
    @Transactional
    public AjaxResult approveReservation(MeetingReservation meetingReservation) {
        MeetingReservation existing = meetingReservationMapper.selectMeetingReservationByReservationId(meetingReservation.getReservationId());
        if (existing == null) {
            return AjaxResult.error("预约记录不存在");
        }
        // 检查会议室状态
        MeetingRoom room = meetingRoomMapper.selectMeetingRoomByRoomId(existing.getRoomId());
        if (room == null || room.getStatus() != 1L) {
            return AjaxResult.error("会议室不可用");
        }

        // 更新状态
        existing.setStatus(meetingReservation.getStatus());
        existing.setUpdateBy(SecurityUtils.getUsername());
        existing.setUpdateTime(DateUtils.getNowDate());

        int result = meetingReservationMapper.updateMeetingReservation(existing);

        if (result > 0) {
            // 发送通知
            sendReservationNotification(existing);
            return AjaxResult.success("审批成功");
        }

        return AjaxResult.error("审批失败");
    }

    private void sendReservationNotification(MeetingReservation reservation) {
        SysNotice notice = new SysNotice();
        notice.setNoticeTitle("会议室预约审批结果");
        notice.setNoticeType("1"); // 通知类型
        notice.setNoticeContent("您的会议室预约(" + reservation.getMeetingTitle() + ")已被" +
                (reservation.getStatus() == 2L ? "批准" : "拒绝"));
        notice.setStatus("0"); // 正常状态
        notice.setCreateBy(SecurityUtils.getUsername());
        notice.setCreateTime(DateUtils.getNowDate());
        noticeService.insertNotice(notice);
    }

    /**
     * 批量审批预约申请
     * @param reservations
     * @return
     */
    @Override
    @Transactional
    public AjaxResult batchApproveReservation(List<MeetingReservation> reservations) {
        int successCount = 0;
        for (MeetingReservation reservation : reservations) {
            AjaxResult result = approveReservation(reservation);
            if (result.isSuccess()) {
                successCount++;
            }
        }
        return AjaxResult.success("批量审批完成，成功处理 " + successCount + " 条记录");
    }

    /**
     * 强制取消预约申请
     * @param reservationIds
     * @return
     */
    @Override
    @Transactional
    public AjaxResult forceCancelReservation(Long[] reservationIds) {
        int successCount = 0;
        for (Long id : reservationIds) {
            MeetingReservation reservation = new MeetingReservation();
            reservation.setReservationId(id);
            reservation.setStatus(0L); // 0-取消
            reservation.setUpdateBy(SecurityUtils.getUsername());
            reservation.setUpdateTime(DateUtils.getNowDate());

            int result = meetingReservationMapper.updateMeetingReservation(reservation);
            if (result > 0) {
                successCount++;
                // 发送取消通知
                MeetingReservation canceled = meetingReservationMapper.selectMeetingReservationByReservationId(id);
                sendCancelNotification(canceled);
            }
        }
        return AjaxResult.success("强制取消完成，成功处理 " + successCount + " 条记录");
    }

    /**
     * 取消预约申请
     * @param reservationId
     * @return
     */
    @Override
    @Transactional
    public int cancelReservation(Long reservationId) {
        MeetingReservation reservation = new MeetingReservation();
        reservation.setReservationId(reservationId);
        reservation.setStatus(0L); // 0表示取消
        reservation.setUpdateBy(SecurityUtils.getUsername());
        reservation.setUpdateTime(DateUtils.getNowDate());

        int result = meetingReservationMapper.updateMeetingReservation(reservation);

        if (result > 0) {
            // 发送取消通知
            MeetingReservation canceled = meetingReservationMapper.selectMeetingReservationByReservationId(reservationId);
            sendCancelNotification(canceled);
        }

        return result;
    }

    /**
     * 用户添加会议室预约申请
     * @param meetingReservation
     * @return
     */
    @Override
    public int userInsertMeetingReservation(MeetingReservation meetingReservation) {
        meetingReservation.setCreateTime(DateUtils.getNowDate());
        return meetingReservationMapper.insertMeetingReservation(meetingReservation);
    }

    /**
     * 查询当前用户的预约列表
     * @param meetingReservation
     * @return
     */
    @Override
    public List<MeetingReservation> selectUserReservationList(MeetingReservation meetingReservation) {
        // 确保只查询当前用户的预约
       if(meetingReservation.getUserId()==null){
           meetingReservation.setUserId(SecurityUtils.getUserId());
       }
       //确保只查询当前用户的预约，强制设置为当前用户ID
        meetingReservation.setUserId(SecurityUtils.getUserId());
        return meetingReservationMapper.selectMeetingReservationList(meetingReservation);
    }

    /**
     * 查询当前用户的预约详情
     * @param reservationId
     * @param userId
     * @return
     */
    @Override
    public MeetingReservation selectUserReservationByReservationId(Long reservationId, Long userId) {
       MeetingReservation reservation = meetingReservationMapper.selectMeetingReservationByReservationId(reservationId);
       if(reservation !=null && !reservation.getUserId().equals(userId)){
           throw new RuntimeException("无权查看此预约信息");
       }
       return reservation;
    }

    /**
     * 用户取消自己的预约
     * @param reservationId
     * @param userId
     * @return
     */
    @Override
    @Transactional
    public int userCancelReservation(Long reservationId, Long userId) {
        MeetingReservation existing = meetingReservationMapper.selectMeetingReservationByReservationId(reservationId);
        if (existing == null) {
            throw new RuntimeException("预约记录不存在");
        }
        if (!existing.getUserId().equals(userId)) {
            throw new RuntimeException("无权取消此预约");
        }
        if (existing.getStatus() == 0L) {
            throw new RuntimeException("预约已取消");
        }

       MeetingReservation update = new MeetingReservation();
        update.setReservationId(reservationId);
        update.setStatus(0L);
        update.setUpdateBy(SecurityUtils.getUsername());
        update.setUpdateTime(DateUtils.getNowDate());

        int result = meetingReservationMapper.updateMeetingReservation(update);

        if (result > 0) {
            sendCancelNotification(existing);
        }

        return result;
    }

    /**
     * 判断会议室是否可用
     * @param roomId
     * @param startTime
     * @param endTime
     * @param excludeReservationId
     * @return
     */
    @Override
    public boolean isRoomAvailable(Long roomId, Date startTime, Date endTime, Long excludeReservationId) {
        //查询该会议室在指定时间段内是否有已批准的预约
       List<MeetingReservation> conflicts= meetingReservationMapper.selectConflictingReservations(roomId, startTime, endTime, excludeReservationId);

       //如果有冲突预约，则返回false
        return conflicts.isEmpty();
    }


    private void sendCancelNotification(MeetingReservation reservation) {
        SysNotice notice = new SysNotice();
        notice.setNoticeTitle("会议室预约被取消");
        notice.setNoticeType("1");
        notice.setNoticeContent("您的会议室预约(" + reservation.getMeetingTitle() + ")已被管理员取消");
        notice.setStatus("0");
        notice.setCreateBy(SecurityUtils.getUsername());
        notice.setCreateTime(DateUtils.getNowDate());
        noticeService.insertNotice(notice);
    }
}
