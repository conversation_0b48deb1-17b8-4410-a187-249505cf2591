package com.ksxz.reserve.controller;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ksxz.common.utils.DateUtils;
import com.ksxz.common.utils.SecurityUtils;
import com.ksxz.common.utils.StringUtils;
import com.ksxz.reserve.service.IConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ksxz.common.annotation.Log;
import com.ksxz.common.core.controller.BaseController;
import com.ksxz.common.core.domain.AjaxResult;
import com.ksxz.common.enums.BusinessType;
import com.ksxz.reserve.domain.MeetingReservation;
import com.ksxz.reserve.service.IMeetingReservationService;
import com.ksxz.common.utils.poi.ExcelUtil;
import com.ksxz.common.core.page.TableDataInfo;
import com.ksxz.framework.websocket.RoomStatusWebSocket;

/**
 * 会议室预约申请Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/reserve/reservation")
public class MeetingReservationController extends BaseController
{
    @Autowired
    private IMeetingReservationService meetingReservationService;

    /**
     * 查询会议室预约申请列表
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeetingReservation meetingReservation)
    {
        startPage();
        List<MeetingReservation> list = meetingReservationService.selectMeetingReservationList(meetingReservation);
        return getDataTable(list);
    }

    /**
     * 导出会议室预约申请列表
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:export')")
    @Log(title = "会议室预约申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeetingReservation meetingReservation)
    {
        List<MeetingReservation> list = meetingReservationService.selectMeetingReservationList(meetingReservation);
        ExcelUtil<MeetingReservation> util = new ExcelUtil<MeetingReservation>(MeetingReservation.class);
        util.exportExcel(response, list, "会议室预约申请数据");
    }

    /**
     * 获取会议室预约申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:query')")
    @GetMapping(value = "/{reservationId}")
    public AjaxResult getInfo(@PathVariable("reservationId") Long reservationId)
    {
        return success(meetingReservationService.selectMeetingReservationByReservationId(reservationId));
    }

    /**
     * 新增会议室预约申请
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:add')")
    @Log(title = "会议室预约申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeetingReservation meetingReservation)
    {
        return toAjax(meetingReservationService.insertMeetingReservation(meetingReservation));
    }

    /**
     * 修改会议室预约申请
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:edit')")
    @Log(title = "会议室预约申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeetingReservation meetingReservation)
    {
        return toAjax(meetingReservationService.updateMeetingReservation(meetingReservation));
    }

    /**
     * 删除会议室预约申请
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:remove')")
    @Log(title = "会议室预约申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reservationIds}")
    public AjaxResult remove(@PathVariable Long[] reservationIds)
    {
        return toAjax(meetingReservationService.deleteMeetingReservationByReservationIds(reservationIds));
    }

    /**
     * 审批会议室预约申请
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:approve')")
    @Log(title = "会议室预约审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody MeetingReservation meetingReservation) {
        return meetingReservationService.approveReservation(meetingReservation);
    }

    /**
     * 批量审批会议室预约申请
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:approve')")
    @Log(title = "会议室批量审批", businessType = BusinessType.UPDATE)
    @PutMapping("/batchApprove")
    public AjaxResult batchApprove(@RequestBody List<MeetingReservation> reservations) {
        return meetingReservationService.batchApproveReservation(reservations);
    }

    /**
     * 强制取消预约
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:cancel')")
    @Log(title = "强制取消预约", businessType = BusinessType.UPDATE)
    @PutMapping("/forceCancel/{reservationIds}")
    public AjaxResult forceCancel(@PathVariable Long[] reservationIds) {
        return meetingReservationService.forceCancelReservation(reservationIds);
    }


    /**
     * 查询我的预约列表
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:list')")
    @GetMapping("/my-list")
    public TableDataInfo myList(MeetingReservation meetingReservation)
    {
        // 获取当前登录用户ID
        Long userId = SecurityUtils.getUserId();
        meetingReservation.setUserId(userId);
        startPage();
        List<MeetingReservation> list = meetingReservationService.selectMeetingReservationList(meetingReservation);
        return getDataTable(list);
    }

    /**
     * 查询当前用户的预约列表
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:myList')")
    @GetMapping("/user/list")
    public TableDataInfo userList(MeetingReservation meetingReservation)
    {
        // 获取当前登录用户ID
        Long userId = SecurityUtils.getUserId();
        meetingReservation.setUserId(userId);
        startPage();
        List<MeetingReservation> list = meetingReservationService.selectUserReservationList(meetingReservation);
        return getDataTable(list);
    }

    /**
     * 获取当前用户的预约详情
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:myQuery')")
    @GetMapping("/user/{reservationId}")
    public AjaxResult getUserReservationInfo(@PathVariable Long reservationId) {
        MeetingReservation reservation = meetingReservationService.selectUserReservationByReservationId(reservationId, SecurityUtils.getUserId());
        return success(reservation);
    }

    /**
     * 导出我的预约列表
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:mylist:export')")
    @Log(title = "我的预约", businessType = BusinessType.EXPORT)
    @PostMapping("/myList/export")
    public void exportMyList(HttpServletResponse response, MeetingReservation meetingReservation)
    {
        // 获取当前登录用户ID
        Long userId = SecurityUtils.getUserId();
        meetingReservation.setUserId(userId);
        List<MeetingReservation> list = meetingReservationService.selectMeetingReservationList(meetingReservation);
        ExcelUtil<MeetingReservation> util = new ExcelUtil<MeetingReservation>(MeetingReservation.class);
        util.exportExcel(response, list, "我的预约数据");
    }


    /**
     * 取消预约
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:cancel')")
    @Log(title = "取消预约", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{reservationId}")
    public AjaxResult cancel(@PathVariable Long reservationId) {
        return toAjax(meetingReservationService.cancelReservation(reservationId));
    }

    /**
     * 用户取消自己的预约
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:myCancel')")
    @Log(title = "用户取消预约", businessType = BusinessType.UPDATE)
    @PutMapping("/user/cancel/{reservationId}")
    public AjaxResult userCancel(@PathVariable Long reservationId) {
        return toAjax(meetingReservationService.userCancelReservation(reservationId, SecurityUtils.getUserId()));
    }

    /**
     * 用户提交会议室预约申请
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:add')")
    @Log(title = "会议室预约申请", businessType = BusinessType.INSERT)
    @PostMapping("/user/add")
    public AjaxResult userAdd(@RequestBody MeetingReservation meetingReservation) {
        // 设置当前登录用户的ID
        Long userId = SecurityUtils.getUserId();
        meetingReservation.setUserId(userId);
        meetingReservation.setUserName(SecurityUtils.getUsername());
        return toAjax(meetingReservationService.insertMeetingReservation(meetingReservation));
    }

    @Autowired
    private IConfigService configService;
    /**
     * 获取自动审批状态
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:config')")
    @GetMapping("/autoApprove/status")
    public AjaxResult getAutoApproveStatus() {
        String status = configService.getConfigValue("meeting.autoApprove.enabled");
        return success("true".equals( status));
    }

    /**
     * 设置自动审批状态
     */
    @PreAuthorize("@ss.hasPermi('reserve:reservation:config')")
    @Log(title = "设置自动审批状态", businessType = BusinessType.UPDATE)
    @PostMapping("/autoApprove/status")
    public AjaxResult setAutoApproveStatus(@RequestBody Map<String, Boolean>  params) {
        Boolean enabled = params.get("enabled");
        configService.setConfigValue("meeting.autoApprove.enabled", enabled?"true":"false");
        return success();
    }
}
