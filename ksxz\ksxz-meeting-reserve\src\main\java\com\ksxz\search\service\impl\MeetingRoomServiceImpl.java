package com.ksxz.search.service.impl;

import java.util.List;
import com.ksxz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ksxz.search.mapper.MeetingRoomMapper;
import com.ksxz.search.domain.MeetingRoom;
import com.ksxz.search.service.IMeetingRoomService;

/**
 * 会议室信息查询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class MeetingRoomServiceImpl implements IMeetingRoomService 
{
    @Autowired
    private MeetingRoomMapper meetingRoomMapper;

    /**
     * 查询会议室信息查询
     * 
     * @param roomId 会议室信息查询主键
     * @return 会议室信息查询
     */
    @Override
    public MeetingRoom selectMeetingRoomByRoomId(Long roomId)
    {
        return meetingRoomMapper.selectMeetingRoomByRoomId(roomId);
    }

    /**
     * 查询会议室信息查询列表
     * 
     * @param meetingRoom 会议室信息查询
     * @return 会议室信息查询
     */
    @Override
    public List<MeetingRoom> selectMeetingRoomList(MeetingRoom meetingRoom)
    {
        return meetingRoomMapper.selectMeetingRoomList(meetingRoom);
    }

    /**
     * 新增会议室信息查询
     * 
     * @param meetingRoom 会议室信息查询
     * @return 结果
     */
    @Override
    public int insertMeetingRoom(MeetingRoom meetingRoom)
    {
        meetingRoom.setCreateTime(DateUtils.getNowDate());
        return meetingRoomMapper.insertMeetingRoom(meetingRoom);
    }

    /**
     * 修改会议室信息查询
     * 
     * @param meetingRoom 会议室信息查询
     * @return 结果
     */
    @Override
    public int updateMeetingRoom(MeetingRoom meetingRoom)
    {
        meetingRoom.setUpdateTime(DateUtils.getNowDate());
        return meetingRoomMapper.updateMeetingRoom(meetingRoom);
    }

    /**
     * 批量删除会议室信息查询
     * 
     * @param roomIds 需要删除的会议室信息查询主键
     * @return 结果
     */
    @Override
    public int deleteMeetingRoomByRoomIds(Long[] roomIds)
    {
        return meetingRoomMapper.deleteMeetingRoomByRoomIds(roomIds);
    }

    /**
     * 删除会议室信息查询信息
     * 
     * @param roomId 会议室信息查询主键
     * @return 结果
     */
    @Override
    public int deleteMeetingRoomByRoomId(Long roomId)
    {
        return meetingRoomMapper.deleteMeetingRoomByRoomId(roomId);
    }
}
