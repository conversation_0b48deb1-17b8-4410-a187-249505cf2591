package com.ksxz.reserve.service.impl;

import com.ksxz.reserve.service.IConfigService;
import com.ksxz.system.mapper.SysConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConfigServiceImpl implements IConfigService {

    @Autowired
    private SysConfigMapper configMapper;

    @Override
    public String getConfigValue(String key) {
        return configMapper.selectConfigValueByKey(key);
    }

    @Override
    public void setConfigValue(String key, String value) {
        configMapper.insertOrUpdateConfig(key, value);
    }
}
