package com.ksxz.reserve.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ksxz.common.annotation.Excel;
import com.ksxz.common.core.domain.BaseEntity;

/**
 * 会议室预约申请对象 meeting_reservation
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class MeetingReservation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 预约ID */
    private Long reservationId;

    /** 会议室ID */
    @Excel(name = "会议室ID")
    private Long roomId;

    /** 申请人ID（关联 sys_user.user_id） */
    private Long userId;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String userName;

    /** 部门ID（关联 sys_dept.dept_id） */
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 会议主题 */
    @Excel(name = "会议主题")
    private String meetingTitle;

    /** 会议开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "会议开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 会议结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "会议结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 参会人员（JSON数组，如 ["张三","李四"]） */
    private String attendees;

    /** 状态：0-取消，1-待审核，2-已通过，3-已拒绝，4-已完成 */
    @Excel(name = "状态：0-取消，1-待审核，2-已通过，3-已拒绝，4-已完成")
    private Long status;

    public Long getAutoApprove() {
        return autoApprove;
    }

    public void setAutoApprove(Long autoApprove) {
        this.autoApprove = autoApprove;
    }

    /** 是否自动审批(0-否，1-是)  */
    @Excel(name="是否自动审批")
    private Long autoApprove;


    private String orderBy;

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public void setReservationId(Long reservationId) 
    {
        this.reservationId = reservationId;
    }

    public Long getReservationId() 
    {
        return reservationId;
    }

    public void setRoomId(Long roomId) 
    {
        this.roomId = roomId;
    }

    public Long getRoomId() 
    {
        return roomId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setMeetingTitle(String meetingTitle) 
    {
        this.meetingTitle = meetingTitle;
    }

    public String getMeetingTitle() 
    {
        return meetingTitle;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setAttendees(String attendees) 
    {
        this.attendees = attendees;
    }

    public String getAttendees() 
    {
        return attendees;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reservationId", getReservationId())
            .append("roomId", getRoomId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("meetingTitle", getMeetingTitle())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("attendees", getAttendees())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
                .append("autoApprove", getAutoApprove())
            .toString();
    }
}
