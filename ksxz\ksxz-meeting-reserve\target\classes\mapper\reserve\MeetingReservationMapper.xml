<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksxz.reserve.mapper.MeetingReservationMapper">
    
    <resultMap type="MeetingReservation" id="MeetingReservationResult">
        <result property="reservationId"    column="reservation_id"    />
        <result property="roomId"    column="room_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="meetingTitle"    column="meeting_title"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="attendees"    column="attendees"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMeetingReservationVo">
        select reservation_id, room_id, user_id, user_name, dept_id, dept_name, meeting_title, start_time, end_time, attendees, status, remark, create_time, update_time from meeting_reservation
    </sql>

    <select id="selectMeetingReservationList" parameterType="MeetingReservation" resultMap="MeetingReservationResult">
        <include refid="selectMeetingReservationVo"/>
        <where>
            <if test="roomId != null">and room_id = #{roomId}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="userName != null and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="deptName != null and deptName != ''">and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="meetingTitle != null and meetingTitle != ''">and meeting_title like concat('%', #{meetingTitle}, '%')</if>
            <if test="status != null">and status = #{status}</if>
            <if test="startTime != null"> and start_time &gt;=#{startTime}</if>
             <if test="endTime != null"> and end_time &lt;= #{endTime}</if>

        </where>
        <!-- 将 order by 放在 where 标签之外 -->
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ORDER BY ${orderBy}
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </select>
    
    <select id="selectMeetingReservationByReservationId" parameterType="Long" resultMap="MeetingReservationResult">
        <include refid="selectMeetingReservationVo"/>
        where reservation_id = #{reservationId}
    </select>
    <select id="selectConflictingReservations" resultMap="MeetingReservationResult">
        <include refid="selectMeetingReservationVo"/>
       where room_id=#{roomId}
       and status =2
       and reservation_id != COALESCE(#{excludeReservationId},-1)
       and ((start_time &lt; #{endTime} and end_time &gt; #{startTime}))
    </select>


    <insert id="insertMeetingReservation" parameterType="MeetingReservation" useGeneratedKeys="true" keyProperty="reservationId">
        insert into meeting_reservation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roomId != null">room_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="meetingTitle != null and meetingTitle != ''">meeting_title,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="attendees != null">attendees,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roomId != null">#{roomId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="meetingTitle != null and meetingTitle != ''">#{meetingTitle},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="attendees != null">#{attendees},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMeetingReservation" parameterType="MeetingReservation">
        update meeting_reservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="meetingTitle != null and meetingTitle != ''">meeting_title = #{meetingTitle},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="attendees != null">attendees = #{attendees},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where reservation_id = #{reservationId}
    </update>

    <delete id="deleteMeetingReservationByReservationId" parameterType="Long">
        delete from meeting_reservation where reservation_id = #{reservationId}
    </delete>

    <delete id="deleteMeetingReservationByReservationIds" parameterType="String">
        delete from meeting_reservation where reservation_id in 
        <foreach item="reservationId" collection="array" open="(" separator="," close=")">
            #{reservationId}
        </foreach>
    </delete>
</mapper>