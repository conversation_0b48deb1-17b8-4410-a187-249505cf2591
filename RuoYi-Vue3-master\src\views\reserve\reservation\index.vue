<template>
  <div class="app-container reservation-management">  
    <!-- 搜索表单区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card">
        <template #header>
          <div class="search-header">
            <span><el-icon><Search /></el-icon> 筛选条件</span>
          </div>
        </template>
        <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form">
          <el-form-item label="会议室" prop="roomId" label-width="60px" style="margin-right: 10px;">
            <el-select
              v-model="queryParams.roomId"
              placeholder="请选择会议室"
              clearable
              style="width: 140px"
            >
              <el-option
                v-for="room in roomList"
                :key="room.roomId"
                :label="room.roomName"
                :value="room.roomId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="申请人" prop="userName" label-width="70px" style="margin-right: 10px;">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入申请人"
              clearable
              @keyup.enter="handleQuery"
              style="width: 140px"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status" style="margin-right: 10px;">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 140px">
              <el-option
                v-for="dict in statusOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围" label-width="80px" style="margin-right: 10px;">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              style="width: 200px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="auto-approve-section">
              <div class="auto-approve-item">
                <span class="auto-approve-label">
                  <el-icon><Setting /></el-icon>
                  自动审批：
                </span>
                <el-switch
                  v-model="autoApproveEnabled"
                  @change="handleAutoApproveChange"
                  :loading="autoApproveLoading"
                  active-text="开启"
                  inactive-text="关闭"
                  active-color="#67C23A"
                  inactive-color="#F56C6C"
                  v-hasPermi="['reserve:reservation:config']"
                />
              </div>
            </div>
      </el-card>
    </div>

    <!-- 工具栏区域 -->
    <!-- <div class="toolbar-section">
      <el-card class="toolbar-card">
        <template #header>
          <div class="toolbar-header">
            <span><el-icon><Setting /></el-icon> 操作工具</span>
          </div>
        </template>

        <el-row :gutter="16" class="toolbar-row"> -->
          <!-- 左侧：批量操作按钮 -->
          <!-- <el-col :span="16">
            <div class="batch-actions">
              <el-button
                type="success"
                icon="Check"
                @click="handleBatchApprove"
                :disabled="ids.length === 0"
                v-hasPermi="['reserve:reservation:approve']"
                class="action-btn"
              >
                <span>批量通过</span>
              </el-button>

              <el-button
                type="warning"
                icon="Close"
                @click="handleBatchReject"
                :disabled="ids.length === 0"
                v-hasPermi="['reserve:reservation:approve']"
                class="action-btn"
              >
                <span>批量拒绝</span>
              </el-button>

              <el-button
                type="danger"
                icon="Delete"
                @click="handleBatchCancel"
                :disabled="ids.length === 0"
                v-hasPermi="['reserve:reservation:cancel']"
                class="action-btn"
              >
                <span>批量取消</span>
              </el-button>
            </div>
          </el-col> -->

          <!-- 右侧：自动审批开关 -->
          <!-- <el-col :span="8">
            <div class="auto-approve-section">
              <div class="auto-approve-item">
                <span class="auto-approve-label">
                  <el-icon><Setting /></el-icon>
                  自动审批：
                </span>
                <el-switch
                  v-model="autoApproveEnabled"
                  @change="handleAutoApproveChange"
                  :loading="autoApproveLoading"
                  active-text="开启"
                  inactive-text="关闭"
                  active-color="#67C23A"
                  inactive-color="#F56C6C"
                  v-hasPermi="['reserve:reservation:config']"
                />
              </div>
              <div class="auto-approve-tip">
                <el-text type="info" size="small">
                  开启后新预约将自动通过审批
                </el-text>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div> -->

    <!-- 数据表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span><el-icon><List /></el-icon> 预约列表</span>
            <div class="table-stats">
              <el-tag type="info" size="small">共 {{ total }} 条记录</el-tag>
            </div>
          </div>
        </template>

        <!-- 空状态 -->
        <div v-if="!loading && reservationList.length === 0" class="empty-state">
          <el-empty description="暂无预约记录">
            <el-button type="primary" @click="handleAdd">立即添加</el-button>
          </el-empty>
        </div>

        <!-- 数据表格 -->
        <el-table
          v-else
          v-loading="loading"
          :data="reservationList"
          class="reservation-table"
          stripe
          border
          :header-cell-style="{ background: '#f8fafc', color: '#606266', fontWeight: '600' }"
          @selection-change="handleSelectionChange"
        >
          <!-- 多选列 -->
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column label="会议室" align="center" prop="roomId" min-width="120">
            <template #default="scope">
              <div class="room-info">
                <el-icon class="room-icon"><OfficeBuilding /></el-icon>
                <span class="room-name">{{ getRoomName(scope.row.roomId) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="申请人" align="center" prop="userName" min-width="100">
            <template #default="scope">
              <div class="user-info">
                <el-icon class="user-icon"><User /></el-icon>
                <span>{{ scope.row.userName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="部门" align="center" prop="deptName" min-width="120">
            <template #default="scope">
              <div class="dept-info">
                <el-icon class="dept-icon"><OfficeBuilding /></el-icon>
                <span>{{ scope.row.deptName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="会议主题" align="center" prop="meetingTitle" min-width="150">
            <template #default="scope">
              <div class="meeting-title">
                <el-icon><Document /></el-icon>
                <span>{{ scope.row.meetingTitle }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="开始时间" align="center" prop="startTime" width="180">
            <template #default="scope">
              <div class="time-info">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="结束时间" align="center" prop="endTime" width="180">
            <template #default="scope">
              <div class="time-info">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
              <el-tag
                :type="statusTagType(scope.row.status)"
                class="status-tag"
                effect="dark"
              >
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
            <template #default="scope">
              <div class="action-buttons">
                <!-- 常用操作：通过和拒绝 -->
                <el-button
                  type="success"
                  size="small"
                  icon="Check"
                  @click="handleApprove(scope.row, 2)"
                  :disabled="!canApprove(scope.row)"
                  v-hasPermi="['reserve:reservation:approve']"
                  class="action-btn-small"
                >通过</el-button>
                
                <el-button
                  type="danger"
                  size="small"
                  icon="Close"
                  @click="handleApprove(scope.row, 3)"
                  :disabled="!canReject(scope.row)"
                  v-hasPermi="['reserve:reservation:approve']"
                  class="action-btn-small"
                >拒绝</el-button>

                <!-- 更多操作下拉菜单 -->
                <el-dropdown @command="handleCommand" trigger="click">
                  <el-button type="primary" size="small" class="action-btn-small">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item 
                        :command="`cancel-${scope.row.reservationId}`"
                        :disabled="!canCancel(scope.row)"
                        @click="handleApprove(scope.row, 0)"
                        v-hasPermi="['reserve:reservation:cancel']"
                      >
                        <el-icon><CircleClose /></el-icon>
                        取消预约
                      </el-dropdown-item>
                      <el-dropdown-item 
                        :command="`delete-${scope.row.reservationId}`"
                        v-hasPermi="['reserve:reservation:remove']"
                        divided
                      >
                        <el-icon><Delete /></el-icon>
                        删除记录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper" v-show="total > 0">
          <pagination
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加或修改会议室预约申请对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="700px"
      append-to-body
      class="reservation-dialog"
      :close-on-click-modal="false"
    >
      <template #header>
        <div class="dialog-header">
          <el-icon><EditPen /></el-icon>
          <span>{{ title }}</span>
        </div>
      </template>

      <el-form ref="reservationRef" :model="form" :rules="rules" label-width="100px" class="reservation-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会议室" prop="roomId">
              <el-select v-model="form.roomId" placeholder="请选择会议室" clearable style="width: 100%">
                <el-option
                  v-for="room in availableRooms"
                  :key="room.roomId"
                  :label="room.roomName"
                  :value="room.roomId"
                >
                  <div class="room-option">
                    <span class="room-name">{{ room.roomName }}</span>
                    <span class="room-capacity">容量: {{ room.capacity }}人</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="申请人姓名" prop="userName">
              <el-select
                v-model="form.userName"
                placeholder="请选择申请人"
                filterable
                clearable
                style="width: 100%"
                @change="onUserChange"
                @focus="handleUserSelectFocus"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.userId"
                  :label="user.userName"
                  :value="user.userName"
                >
                  <div class="user-option">
                    <span class="user-name">{{ user.userName }}</span>
                    <span class="user-dept">{{ user.deptName }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人ID" prop="userId">
              <el-input v-model="form.userId" placeholder="请输入申请人ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门ID" prop="deptId">
              <el-input v-model="form.deptId" placeholder="请输入部门ID" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会议主题" prop="meetingTitle">
              <el-input
                v-model="form.meetingTitle"
                placeholder="请输入会议主题"
                :prefix-icon="Document"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                clearable
                v-model="form.startTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                placeholder="请选择会议开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                clearable
                v-model="form.endTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                placeholder="请选择会议结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="参会人员" prop="attendees">
              <el-input
                v-model="form.attendees"
                type="textarea"
                placeholder="请输入参会人员，多人请用逗号分隔"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="审批意见" prop="approveComment">
              <el-input
                v-model="form.approveComment"
                type="textarea"
                placeholder="请输入审批意见（可选）"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入备注信息（可选）"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" size="large">取 消</el-button>
          <el-button type="primary" @click="submitForm" size="large">
            <el-icon><Check /></el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Reservation">
import { ref, reactive, toRefs, getCurrentInstance, watch, computed } from "vue";
import {
  listReservation, getReservation, delReservation,
  addReservation, updateReservation, approveReservation,
  batchApprove, forceCancel, getAutoApproveStatus, setAutoApproveStatus
} from "@/api/reserve/reservation";
import { ArrowDown, Delete, Close, CircleClose } from '@element-plus/icons-vue'
import { listUser } from "@/api/system/user";
import { listRoom } from "@/api/search/room";
import {
  Setting,
  Search,
  List,
  OfficeBuilding,
  Document,
  Clock,
  EditPen,
  Check,
  User
} from '@element-plus/icons-vue';

// Vue实例代理
const { proxy } = getCurrentInstance();

// 状态选项
const statusOptions = [
  { dictLabel: '已取消', dictValue: '0' },
  { dictLabel: '待审核', dictValue: '1' },
  { dictLabel: '已通过', dictValue: '2' },
  { dictLabel: '已拒绝', dictValue: '3' },
  { dictLabel: '已完成', dictValue: '4' }
]

// UI相关
const reservationList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const total = ref(0)
const title = ref("")
const activeTab = ref("all")
const myReservationListData = ref([])
//用户列表数据
const userList = ref([])
const userLoading = ref(false)
//会议室列表数据
const roomList = ref([])
const roomLoading = ref(false)
//时间范围
const dateRange = ref([])

// 自动审批相关
const autoApproveEnabled = ref(false)
const autoApproveLoading = ref(false)

//仅显示 status 为 1 的会议室
const availableRooms = computed(() => {
  console.log(roomList.value)
  return roomList.value.filter(room => room.status === 1)
})

// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roomId: null,
    status: null,
    userName: null,
    startTime: null,    // 开始时间参数
    endTime: null       // 结束时间参数
  },
  rules: {
    roomId: [{ required: true, message: "会议室不能为空", trigger: "blur" }],
    userId: [{ required: true, message: "申请人ID不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "申请人姓名不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "部门ID不能为空", trigger: "blur" }],
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    meetingTitle: [{ required: true, message: "会议主题不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "会议开始时间不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "会议结束时间不能为空", trigger: "blur" }],
  }
})

const { queryParams, form, rules } = toRefs(data)

// ========== 核心方法 ==========

function getList() {
  loading.value = true
  listReservation(queryParams.value).then(response => {
    console.log('📥 获取的 response：', response)
    reservationList.value = response.rows
    console.log("预约的数据",response.rows)
    total.value = response.total
    loading.value = false
  })
}

function getMyList() {
  (queryParams.value).then(response => {
    myReservationListData.value = response.rows
    total.value = response.total
  })
}

/** 状态格式化 */
function formatStatus(status) {
  const map = { 0: '已取消', 1: '待审核', 2: '已通过', 3: '已拒绝', 4: '已完成' }
  return map[status] || '未知'
}

/** 状态标签类型 */
function statusTagType(status) {
  const map = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger', 4: '' }
  return map[status]
}

function handleApprove(row, status) {
  proxy.$confirm(`确认要${status === 2 ? '通过' : '拒绝'}该预约吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const data = {
      reservationId: row.reservationId,
      status: status,
      approveComment: `${status === 2 ? '通过' : '拒绝'}审批`
    }
    return approveReservation(data)
  }).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getList()
  }).catch(error => {
    if (error !== "cancel") {
      proxy.$modal.msgError("操作失败: " + (error.message || error))
    }
  })
}

// function handleBatchApprove() {
//   if (ids.value.length === 0) {
//     proxy.$modal.msgWarning("请选择要批量通过的预约")
//     return
//   }
//   proxy.$confirm("确认要批量通过选中的预约吗?", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   }).then(() => {
//     const promises = ids.value.map(id => {
//       return approveReservation({
//         reservationId: id,
//         status: 2,
//         approveComment: "批量通过审批"
//       })
//     })
//     return Promise.all(promises)
//   }).then(() => {
//     proxy.$modal.msgSuccess("批量通过操作成功")
//     getList()
//   }).catch(error => {
//     if (error !== "cancel") {
//       proxy.$modal.msgError("操作失败: " + (error.message || error))
//     }
//   })
// }

// function handleBatchReject() {
//   if (ids.value.length === 0) {
//     proxy.$modal.msgWarning("请选择要批量拒绝的预约")
//     return
//   }
//   proxy.$confirm("确认要批量拒绝选中的预约吗?", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   }).then(() => {
//     const promises = ids.value.map(id => {
//       return approveReservation({
//         reservationId: id,
//         status: 3,
//         approveComment: "批量拒绝审批"
//       })
//     })
//     return Promise.all(promises)
//   }).then(() => {
//     proxy.$modal.msgSuccess("批量拒绝操作成功")
//     getList()
//   }).catch(error => {
//     if (error !== "cancel") {
//       proxy.$modal.msgError("操作失败: " + (error.message || error))
//     }
//   })
// }

// function handleForceCancel(row) {
//   const _reservationIds = row ? row.reservationId : ids.value
//   proxy.$confirm(`确认要${row ? '取消' : '批量取消'}选中的预约吗?`, "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   }).then(() => {
//     return approveReservation({
//       reservationId: _reservationIds,
//       status: 0,
//       approveComment: "强制取消预约"
//     })
//   }).then(() => {
//     proxy.$modal.msgSuccess("取消操作成功")
//     getList()
//   }).catch(error => {
//     if (error !== "cancel") {
//       proxy.$modal.msgError("操作失败: " + (error.message || error))
//     }
//   })
// }

function cancel() {
  open.value = false
  reset()
}

// ========== 新增的自动审批相关方法 ==========

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.reservationId)
}

/** 批量取消预约 */
// function handleBatchCancel() {
//   if (ids.value.length === 0) {
//     proxy.$modal.msgWarning("请选择要批量取消的预约")
//     return
//   }
//   proxy.$confirm("确认要批量取消选中的预约吗?", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   }).then(() => {
//     return forceCancel(ids.value.join(','))
//   }).then(() => {
//     proxy.$modal.msgSuccess("批量取消操作成功")
//     getList()
//     ids.value = []
//   }).catch(error => {
//     if (error !== "cancel") {
//       proxy.$modal.msgError("操作失败: " + (error.message || error))
//     }
//   })
// }

/** 获取自动审批状态 */
function loadAutoApproveStatus() {
  autoApproveLoading.value = true
  getAutoApproveStatus().then(response => {
    autoApproveEnabled.value = response.data
  }).catch(error => {
    console.error('获取自动审批状态失败:', error)
    proxy.$modal.msgError("获取自动审批状态失败")
  }).finally(() => {
    autoApproveLoading.value = false
  })
}

/** 自动审批开关变化处理 */
function handleAutoApproveChange(value) {
  autoApproveLoading.value = true
  setAutoApproveStatus(value).then(() => {
    proxy.$modal.msgSuccess(`自动审批已${value ? '开启' : '关闭'}`)
  }).catch(error => {
    // 如果设置失败，恢复原状态
    autoApproveEnabled.value = !value
    console.error('设置自动审批状态失败:', error)
    proxy.$modal.msgError("设置自动审批状态失败")
  }).finally(() => {
    autoApproveLoading.value = false
  })
}

function reset() {
  form.value = {
    reservationId: null,
    roomId: null,
    roomName: null,
    userId: null,
    userName: null,
    deptId: null,
    deptName: null,
    meetingTitle: null,
    startTime: null,
    endTime: null,
    attendees: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("reservationRef")
}

function handleQuery() {
  queryParams.value.pageNum = 1
  
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0] + ' 00:00:00'  
    queryParams.value.endTime = dateRange.value[1] + ' 23:59:59'   
  } else {
    queryParams.value.startTime = null   // 改为 startTime
    queryParams.value.endTime = null
  }
  
  // 清除旧的参数名（如果存在）
  queryParams.value.beginTime = null
  
  console.log('🔍 修正后的查询参数:', queryParams.value)
  getList()
}


function resetQuery() {
  dateRange.value = []  // 重置日期范围
  proxy.resetForm("queryRef")
  handleQuery()
}

function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会议室预约申请"
}

function submitForm() {
  proxy.$refs["reservationRef"].validate(valid => {
    if (valid) {
      console.log('🔍 提交前的 form.value:', form.value)
      console.log('🔍 startTime 类型:', typeof form.value.startTime, form.value.startTime)
      console.log('🔍 endTime 类型:', typeof form.value.endTime, form.value.endTime)

      form.value.roomId = Number(form.value.roomId);

      if (form.value.reservationId != null) {
        updateReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        console.log('将要进入add预约的form.value', form.value)
        addReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

function handleDelete(row) {
  const _reservationIds = row.reservationId || ids.value
  proxy.$modal.confirm(`是否确认删除会议室预约申请编号为 "${_reservationIds}" 的数据项？`)
    .then(() => delReservation(_reservationIds))
    .then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
    }).catch(() => {})
}

/** 禁用过去的日期 */
function disabledDate(time) {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天之前的日期
}

// 获取用户列表的方法
async function getUserList() {
  try {
    userLoading.value = true
    
    // 调用用户列表API，获取所有活跃用户
    const response = await listUser({
      pageNum: 1,
      pageSize: 1000,  // 获取所有用户
      status: '0'      // 只获取正常状态的用户
    })
    
    userList.value = response.rows || []
    console.log('获取到用户列表:', userList.value) // 调试用
    
  } catch (error) {
    console.error('获取用户列表失败:', error)
    proxy.$modal.msgError('获取用户列表失败')
    userList.value = []
  } finally {
    userLoading.value = false
  }
}

// 用户选择变化时的处理方法
function onUserChange(userName) {
  if (!userName) {
    // 清空时重置相关字段
    form.value.userId = null
    form.value.deptId = null
    form.value.deptName = null
    return
  }
  
  // 根据用户名找到对应的用户信息
  const selectedUser = userList.value.find(user => user.userName === userName)
  
  if (selectedUser) {
    // 自动填写关联字段
    form.value.userId = selectedUser.userId
    form.value.deptId = selectedUser.deptId
    form.value.deptName = selectedUser.dept?.deptName || selectedUser.deptName || '未知部门'
    
    console.log('选择的用户信息:', selectedUser) // 调试用
    
    // 显示提示信息
    proxy.$modal.msgSuccess(`已选择用户：${selectedUser.userName} (${form.value.deptName})`)
  } else {
    proxy.$modal.msgWarning('未找到用户信息')
  }
}

// 处理用户选择框获得焦点
function handleUserSelectFocus() {
  // 如果用户列表为空，则加载用户列表
  if (userList.value.length === 0) {
    getUserList()
  }
}

// 获取会议室列表的方法 
async function getRoomList() {
  try {
    roomLoading.value = true
    
    console.log('开始获取会议室列表...')
    
    // 修改：使用空对象作为参数，和工作正常的文件保持一致
    const response = await listRoom({pageNum: 1, pageSize: 1000})
    
    console.log('会议室API响应:', response)
    roomList.value = response.rows || response.data || []
    
    console.log('最终会议室列表:', roomList.value)
    
  } catch (error) {
    console.error('获取会议室列表失败:', error)
    proxy.$modal.msgError('获取会议室列表失败')
    roomList.value = []
  } finally {
    roomLoading.value = false
  }
}

/** 根据会议室ID获取会议室名称 */
function getRoomName(roomId) {
  const room = roomList.value.find(r => r.roomId == roomId)
  return room ? room.roomName : `会议室${roomId}`
}


/** 判断是否可以通过 */
function canApprove(row) {
  return row.status == 1 // 只有待审核状态可以通过
}

/** 判断是否可以拒绝 */
function canReject(row) {
  return row.status == 1 // 只有待审核状态可以拒绝
}

/** 判断是否可以取消 */
function canCancel(row) {
  return row.status != 0 // 除了已取消状态，其他都可以取消
}

// 处理下拉菜单命令
function handleCommand(command) {
  const [action, id] = command.split('-')
  const row = reservationList.value.find(item => item.reservationId == id)
  
  if (!row) return
  
  switch (action) {
    case 'cancel':
      handleForceCancel(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}



// 监听 tab 切换
watch(activeTab, (val) => {
  if (val === 'my') {
    getMyList()
  }
})

// 初始化数据
getList()
getUserList()
getRoomList()
loadAutoApproveStatus()
</script>

<style scoped lang="scss">
.reservation-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 页面标题区域 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px 40px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .el-icon {
    margin-right: 12px;
    font-size: 32px;
  }
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.search-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.search-form {
  padding: 8px 0;

  .el-form-item {
    margin-bottom: 16px;
  }
}

.search-form-grid {
  display: grid;
  grid-template-columns: auto 1fr 1fr 1fr 1fr;
  // gap: 10px;
  align-items: end;
  // padding-bottom: 16px;
  border-radius: 6px;
}

.search-buttons {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.search-form-grid .el-form-item {
  margin: 0 !important;
}


/* 工具栏区域 */
// .toolbar-section {
//   margin-bottom: 20px;
// }

// .toolbar-card {
//   border-radius: 12px;
//   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
//   border: none;
//   overflow: hidden;
// }

// .toolbar-row {
//   align-items: center;
// }

// .action-btn {
//   border-radius: 8px;
//   font-weight: 500;
//   padding: 10px 20px;
//   transition: all 0.3s ease;
//   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

//   &:hover {
//     transform: translateY(-2px);
//     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
//   }

//   span {
//     margin-left: 4px;
//   }
// }

// /* 批量操作区域 */
// .batch-actions {
//   display: flex;
//   gap: 12px;
//   flex-wrap: wrap;
// }

/* 自动审批区域 */
.auto-approve-section {
  display: flex;
  flex-direction: column;
  // align-items: flex-end;
  gap: 8px;
  // margin-top: 12px;
}
// .auto-approve-section {
//   display: flex;
//   flex-direction: column;
//   align-items: flex-end;
//   gap: 8px;
// }

.auto-approve-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auto-approve-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.auto-approve-tip {
  text-align: right;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.table-stats {
  display: flex;
  gap: 8px;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 表格样式 */
.reservation-table {
  border-radius: 8px;
  overflow: hidden;

  :deep(.el-table__header) {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  }

  :deep(.el-table__row) {
    transition: all 0.3s ease;

    &:hover {
      background-color: #f0f9ff !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }

  :deep(.el-table__cell) {
    border-color: #f1f5f9;
    padding: 16px 12px;
  }
}

.room-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .room-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 16px;
  }

  .room-name {
    font-weight: 500;
    color: #303133;
  }
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .user-icon {
    margin-right: 8px;
    color: #67c23a;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #303133;
  }
}

.dept-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .dept-icon {
    margin-right: 8px;
    color: #e6a23c;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #303133;
  }
}

.meeting-title {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    margin-right: 8px;
    color: #67c23a;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #303133;
  }
}

.time-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .time-icon {
    margin-right: 8px;
    color: #e6a23c;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #606266;
    font-size: 13px;
  }
}

.status-tag {
  font-weight: 600;
  border-radius: 6px;
  padding: 4px 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: nowrap; /* 强制一行显示 */
    :deep(.el-button) {
    margin: 0 !important; /* 强制清除 Element Plus 默认 margin */
  }
}

.action-btn-small {
  font-size: 12px;
  border-radius: 4px;
  font-weight: 400;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  min-width: 52px;
  flex-shrink: 0; /* 防止按钮压缩 */
  
  /* 简约配色 */
  &.el-button--primary {
    background: #409eff;
    border-color: #409eff;
    color: white;
    
    &:hover:not(:disabled) {
      background: #66b1ff;
      border-color: #66b1ff;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--success {
    background: #67c23a;
    border-color: #67c23a;
    color: white;
    
    &:hover:not(:disabled) {
      background: #85ce61;
      border-color: #85ce61;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--warning {
    background: #e6a23c;
    border-color: #e6a23c;
    color: white;
    
    &:hover:not(:disabled) {
      background: #ebb563;
      border-color: #ebb563;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--danger {
    background: #f56c6c;
    border-color: #f56c6c;
    color: white;
    
    &:hover:not(:disabled) {
      background: #f78989;
      border-color: #f78989;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }

  &.el-button--info {
    background: #909399;
    border-color: #909399;
    color: white;
    
    &:hover:not(:disabled) {
      background: #a6a9ad;
      border-color: #a6a9ad;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  /* 移除阴影和变换效果，保持简约 */
  &:hover:not(:disabled) {
    transform: none;
    box-shadow: none;
  }
  
  /* 图标样式 */
  .el-icon {
    margin-right: 4px;
    font-size: 12px;
  }
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: center;
}

/* 对话框样式 */
.reservation-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 32px 24px;
    background: #fafbfc;
  }

  :deep(.el-dialog__footer) {
    background: white;
    padding: 20px 24px;
    border-top: 1px solid #f1f5f9;
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;

  .el-icon {
    margin-right: 12px;
    font-size: 20px;
  }
}

.reservation-form {
  .el-form-item {
    margin-bottom: 20px;

    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-textarea__inner) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-date-editor) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

.room-option {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .room-name {
    font-weight: 500;
    color: #303133;
  }

  .room-capacity {
    font-size: 12px;
    color: #909399;
    background: #f5f7fa;
    padding: 2px 8px;
    border-radius: 4px;
  }
}

.user-option {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .user-name {
    font-weight: 500;
    color: #303133;
  }

  .user-dept {
    font-size: 12px;
    color: #909399;
    background: #f5f7fa;
    padding: 2px 8px;
    border-radius: 4px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    border-radius: 8px;
    font-weight: 500;
    padding: 12px 24px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .reservation-management {
    padding: 16px;
  }

  .page-header {
    padding: 24px 32px;
  }

  .page-title {
    font-size: 24px;

    .el-icon {
      font-size: 28px;
    }
  }
}

@media (max-width: 768px) {
  .reservation-management {
    padding: 12px;
  }

  .page-header {
    padding: 20px 24px;
  }

  .page-title {
    font-size: 20px;
    flex-direction: column;
    text-align: center;

    .el-icon {
      font-size: 24px;
      margin-right: 0;
      margin-bottom: 8px;
    }
  }

  .page-description {
    font-size: 14px;
    text-align: center;
  }

  .search-form {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
      margin-bottom: 16px;

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }

  // .toolbar-row {
  //   flex-direction: column;
  //   gap: 12px;

  //   .el-col {
  //     width: 100%;
  //   }
  // }

  .action-buttons {
    flex-direction: column;
    gap: 6px;

    .action-btn-small {
      width: 100%;
    }
  }

  .reservation-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 20px 16px;
    }
  }

  .reservation-form {
    .el-row {
      .el-col {
        width: 100%;
      }
    }
  }

  .dialog-footer {
    flex-direction: column;

    .el-button {
      width: 100%;
      margin: 0;
    }
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-section,
// .toolbar-section,
.table-section {
  animation: fadeInUp 0.6s ease-out;
}

.search-section {
  animation-delay: 0.1s;
}

// .toolbar-section {
//   animation-delay: 0.2s;
// }

.table-section {
  animation-delay: 0.3s;
}

/* 自定义滚动条 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
</style>
