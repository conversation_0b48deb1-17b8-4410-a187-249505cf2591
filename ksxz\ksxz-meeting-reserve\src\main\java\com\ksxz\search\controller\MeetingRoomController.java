package com.ksxz.search.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ksxz.common.annotation.Log;
import com.ksxz.common.core.controller.BaseController;
import com.ksxz.common.core.domain.AjaxResult;
import com.ksxz.common.enums.BusinessType;
import com.ksxz.search.domain.MeetingRoom;
import com.ksxz.search.service.IMeetingRoomService;
import com.ksxz.common.utils.poi.ExcelUtil;
import com.ksxz.common.core.page.TableDataInfo;

/**
 * 会议室信息查询Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/search/room")
public class MeetingRoomController extends BaseController
{
    @Autowired
    private IMeetingRoomService meetingRoomService;

    /**
     * 查询会议室信息查询列表
     */
    @PreAuthorize("@ss.hasPermi('search:room:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeetingRoom meetingRoom)
    {
        startPage();
        List<MeetingRoom> list = meetingRoomService.selectMeetingRoomList(meetingRoom);
        return getDataTable(list);
    }

    /**
     * 导出会议室信息查询列表
     */
    @PreAuthorize("@ss.hasPermi('search:room:export')")
    @Log(title = "会议室信息查询", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeetingRoom meetingRoom)
    {
        List<MeetingRoom> list = meetingRoomService.selectMeetingRoomList(meetingRoom);
        ExcelUtil<MeetingRoom> util = new ExcelUtil<MeetingRoom>(MeetingRoom.class);
        util.exportExcel(response, list, "会议室信息查询数据");
    }

    /**
     * 获取会议室信息查询详细信息
     */
    @PreAuthorize("@ss.hasPermi('search:room:query')")
    @GetMapping(value = "/{roomId}")
    public AjaxResult getInfo(@PathVariable("roomId") Long roomId)
    {
        return success(meetingRoomService.selectMeetingRoomByRoomId(roomId));
    }

    /**
     * 新增会议室信息查询
     */
    @PreAuthorize("@ss.hasPermi('search:room:add')")
    @Log(title = "会议室信息查询", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeetingRoom meetingRoom)
    {
        return toAjax(meetingRoomService.insertMeetingRoom(meetingRoom));
    }

    /**
     * 修改会议室信息查询
     */
    @PreAuthorize("@ss.hasPermi('search:room:edit')")
    @Log(title = "会议室信息查询", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeetingRoom meetingRoom)
    {
        return toAjax(meetingRoomService.updateMeetingRoom(meetingRoom));
    }

    /**
     * 删除会议室信息查询
     */
    @PreAuthorize("@ss.hasPermi('search:room:remove')")
    @Log(title = "会议室信息查询", businessType = BusinessType.DELETE)
	@DeleteMapping("/{roomIds}")
    public AjaxResult remove(@PathVariable Long[] roomIds)
    {
        return toAjax(meetingRoomService.deleteMeetingRoomByRoomIds(roomIds));
    }
}
